import { useEffect } from 'react';
import { useRouter } from 'next/router';

const AdminIndex = () => {
  const router = useRouter();

  useEffect(() => {
    // توجيه مباشر للوحة التحكم (تم تعطيل المصادقة مؤقتاً)
    router.push('/admin/dashboard');
  }, [router]);

  // عرض شاشة تحميل أثناء التوجيه
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">جاري التحميل...</p>
        <p className="text-sm text-gray-500 mt-2">تم تعطيل المصادقة مؤقتاً</p>
      </div>
    </div>
  );
};

export default AdminIndex;
