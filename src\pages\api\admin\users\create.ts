import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { executeQuery, executeQuerySingle } from '../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // التحقق من المصادقة
    const token = extractToken(req);
    if (!token) {
      return res.status(401).json({ 
        message: 'غير مصرح لك بالوصول',
        success: false 
      });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({ 
        message: 'رمز المصادقة غير صحيح',
        success: false 
      });
    }

    const { username, email, password } = req.body;

    // التحقق من وجود البيانات المطلوبة
    if (!username || !email || !password) {
      return res.status(400).json({ 
        message: 'جميع الحقول مطلوبة',
        success: false 
      });
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ 
        message: 'البريد الإلكتروني غير صحيح',
        success: false 
      });
    }

    // التحقق من قوة كلمة المرور
    if (password.length < 6) {
      return res.status(400).json({ 
        message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
        success: false 
      });
    }

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    const existingUser = await executeQuerySingle(
      'SELECT id FROM admins WHERE username = ? AND deleted_at IS NULL',
      [username]
    );

    if (existingUser) {
      return res.status(409).json({ 
        message: 'اسم المستخدم موجود بالفعل',
        success: false 
      });
    }

    // التحقق من عدم وجود البريد الإلكتروني مسبقاً
    const existingEmail = await executeQuerySingle(
      'SELECT id FROM admins WHERE email = ? AND deleted_at IS NULL',
      [email]
    );

    if (existingEmail) {
      return res.status(409).json({ 
        message: 'البريد الإلكتروني موجود بالفعل',
        success: false 
      });
    }

    // تشفير كلمة المرور
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // إدراج المستخدم الجديد
    const result = await executeQuery(
      `INSERT INTO admins (username, email, password_hash, is_active, created_at, updated_at) 
       VALUES (?, ?, ?, 1, NOW(), NOW())`,
      [username, email, passwordHash]
    );

    // الحصول على بيانات المستخدم الجديد
    const newUser = await executeQuerySingle(
      'SELECT id, username, email, is_active, created_at FROM admins WHERE username = ?',
      [username]
    );

    return res.status(201).json({
      success: true,
      message: 'تم إنشاء المستخدم بنجاح',
      user: newUser
    });

  } catch (error) {
    console.error('Create user error:', error);
    return res.status(500).json({ 
      message: 'حدث خطأ في الخادم',
      success: false 
    });
  }
}
