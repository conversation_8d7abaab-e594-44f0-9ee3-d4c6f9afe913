import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { executeQuery, executeQuerySingle } from '../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // التحقق من المصادقة
    const token = extractToken(req);
    if (!token) {
      return res.status(401).json({ 
        message: 'غير مصرح لك بالوصول',
        success: false 
      });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({ 
        message: 'رمز المصادقة غير صحيح',
        success: false 
      });
    }

    const { userId, username, email } = req.body;

    // التحقق من وجود البيانات المطلوبة
    if (!userId || !username || !email) {
      return res.status(400).json({ 
        message: 'جميع الحقول مطلوبة',
        success: false 
      });
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ 
        message: 'البريد الإلكتروني غير صحيح',
        success: false 
      });
    }

    // التحقق من وجود المستخدم
    const existingUser = await executeQuerySingle(
      'SELECT id FROM admins WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!existingUser) {
      return res.status(404).json({ 
        message: 'المستخدم غير موجود',
        success: false 
      });
    }

    // التحقق من عدم وجود اسم المستخدم مع مستخدم آخر
    const duplicateUsername = await executeQuerySingle(
      'SELECT id FROM admins WHERE username = ? AND id != ? AND deleted_at IS NULL',
      [username, userId]
    );

    if (duplicateUsername) {
      return res.status(409).json({ 
        message: 'اسم المستخدم موجود بالفعل',
        success: false 
      });
    }

    // التحقق من عدم وجود البريد الإلكتروني مع مستخدم آخر
    const duplicateEmail = await executeQuerySingle(
      'SELECT id FROM admins WHERE email = ? AND id != ? AND deleted_at IS NULL',
      [email, userId]
    );

    if (duplicateEmail) {
      return res.status(409).json({ 
        message: 'البريد الإلكتروني موجود بالفعل',
        success: false 
      });
    }

    // تحديث بيانات المستخدم
    await executeQuery(
      'UPDATE admins SET username = ?, email = ?, updated_at = NOW() WHERE id = ?',
      [username, email, userId]
    );

    // الحصول على بيانات المستخدم المحدثة
    const updatedUser = await executeQuerySingle(
      'SELECT id, username, email, is_active, last_login, created_at, updated_at FROM admins WHERE id = ?',
      [userId]
    );

    return res.status(200).json({
      success: true,
      message: 'تم تحديث البيانات بنجاح',
      user: updatedUser
    });

  } catch (error) {
    console.error('Update user error:', error);
    return res.status(500).json({ 
      message: 'حدث خطأ في الخادم',
      success: false 
    });
  }
}
