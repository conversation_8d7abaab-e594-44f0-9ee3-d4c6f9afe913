import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { executeQuerySingle } from '../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  is_active: boolean;
  last_login: Date | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { username, password } = req.body;

    // التحقق من وجود البيانات المطلوبة
    if (!username || !password) {
      return res.status(400).json({ 
        message: 'اسم المستخدم وكلمة المرور مطلوبان',
        success: false 
      });
    }

    // البحث عن المستخدم في قاعدة البيانات
    const admin = await executeQuerySingle<AdminUser>(
      'SELECT * FROM admins WHERE username = ? AND is_active = 1 AND deleted_at IS NULL',
      [username]
    );

    if (!admin) {
      return res.status(401).json({ 
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
        success: false 
      });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, admin.password_hash);
    
    if (!isValidPassword) {
      return res.status(401).json({ 
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
        success: false 
      });
    }

    // تحديث وقت آخر تسجيل دخول
    await executeQuerySingle(
      'UPDATE admins SET last_login = NOW(), updated_at = NOW() WHERE id = ?',
      [admin.id]
    );

    // إنشاء JWT token
    const token = jwt.sign(
      { 
        userId: admin.id,
        username: admin.username,
        email: admin.email,
        role: 'admin'
      },
      JWT_SECRET,
      { 
        expiresIn: '24h',
        issuer: 'droobhajer-admin',
        audience: 'droobhajer-admin-panel'
      }
    );

    // إعداد cookie آمن
    res.setHeader('Set-Cookie', [
      `authToken=${token}; HttpOnly; Path=/; Max-Age=86400; SameSite=Strict${
        process.env.NODE_ENV === 'production' ? '; Secure' : ''
      }`
    ]);

    // إرجاع بيانات المستخدم بدون كلمة المرور
    return res.status(200).json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: admin.id,
        username: admin.username,
        email: admin.email,
        role: 'admin',
        lastLogin: admin.last_login
      },
      token
    });

  } catch (error) {
    console.error('Admin login error:', error);
    return res.status(500).json({ 
      message: 'حدث خطأ في الخادم',
      success: false 
    });
  }
}
