import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import { generateToken } from '../../../lib/auth';
import { executeQuerySingle } from '../../../lib/database-config';
import { serialize } from 'cookie';
import {
  checkRateLimit,
  RATE_LIMIT_CONFIGS,
  getClientIP,
  isIPBlocked,
  logSuspiciousActivity
} from '../../../lib/rate-limiter';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  is_active: boolean;
  last_login: Date | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { username, password } = req.body;

    console.log('🔐 محاولة تسجيل دخول:', { username, passwordLength: password?.length });

    // التحقق من وجود البيانات المطلوبة
    if (!username || !password) {
      console.log('❌ بيانات ناقصة');
      return res.status(400).json({
        message: 'اسم المستخدم وكلمة المرور مطلوبان',
        success: false
      });
    }

    // البحث عن المستخدم في قاعدة البيانات
    console.log('🔍 البحث عن المستخدم في قاعدة البيانات...');
    const admin = await executeQuerySingle<AdminUser>(
      'SELECT * FROM admins WHERE username = ? AND is_active = 1 AND deleted_at IS NULL',
      [username]
    );

    console.log('👤 نتيجة البحث:', admin ? 'تم العثور على المستخدم' : 'لم يتم العثور على المستخدم');

    if (!admin) {
      console.log('❌ المستخدم غير موجود أو غير نشط');
      return res.status(401).json({
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
        success: false
      });
    }

    // التحقق من كلمة المرور
    console.log('🔐 التحقق من كلمة المرور...');
    const isValidPassword = await bcrypt.compare(password, admin.password_hash);
    console.log('🔑 نتيجة التحقق من كلمة المرور:', isValidPassword ? 'صحيحة' : 'خاطئة');

    if (!isValidPassword) {
      console.log('❌ كلمة المرور غير صحيحة');
      return res.status(401).json({
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
        success: false
      });
    }

    // تحديث وقت آخر تسجيل دخول
    console.log('📝 تحديث وقت آخر تسجيل دخول...');
    await executeQuerySingle(
      'UPDATE admins SET last_login = NOW(), updated_at = NOW() WHERE id = ?',
      [admin.id]
    );

    // إنشاء JWT token
    console.log('🎫 إنشاء JWT token...');
    const token = jwt.sign(
      {
        userId: admin.id,
        username: admin.username,
        email: admin.email,
        role: 'admin'
      },
      JWT_SECRET,
      {
        expiresIn: '24h',
        issuer: 'droobhajer-admin',
        audience: 'droobhajer-admin-panel'
      }
    );

    // إعداد cookie آمن
    res.setHeader('Set-Cookie', [
      `authToken=${token}; HttpOnly; Path=/; Max-Age=86400; SameSite=Strict${
        process.env.NODE_ENV === 'production' ? '; Secure' : ''
      }`
    ]);

    console.log('✅ تم تسجيل الدخول بنجاح للمستخدم:', admin.username);

    // إرجاع بيانات المستخدم بدون كلمة المرور
    return res.status(200).json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: admin.id,
        username: admin.username,
        email: admin.email,
        role: 'admin',
        lastLogin: admin.last_login
      },
      token
    });

  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error);
    return res.status(500).json({
      message: 'حدث خطأ في الخادم',
      success: false,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
